package com.laien.cmsapp.oog101.controller;

import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog101.request.ProjSevenmChallengeListReq;
import com.laien.cmsapp.oog101.response.ProjSevenmChallengeVO;
import com.laien.cmsapp.oog101.service.IProjSevenmChallengeService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 7M Challenge 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025/01/02
 */
@Api(tags = "app端：7M Challenge")
@RestController
@RequestMapping("/{appCode}/sevenmChallenge")
public class ProjSevenmChallengeController extends ResponseController {

    @Resource
    private IProjSevenmChallengeService challengeService;

    @ApiOperation(value = "Challenge列表")
    @GetMapping("/v1/list")
    public ResponseResult<List<ProjSevenmChallengeVO>> list(ProjSevenmChallengeListReq req) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        List<ProjSevenmChallengeVO> result = challengeService.list(versionInfoBO, req);
        return succ(result);
    }
}
